'use client'

import { 
  Smartphone, 
  Car, 
  Home, 
  Briefcase, 
  Calendar,
  MapPin,
  DollarSign,
  Clock,
  Users,
  Building,
  Wifi,
  Car as CarIcon,
  Fuel,
  Settings,
  Camera,
  HardDrive,
  Cpu,
  Battery,
  Monitor,
  Bed,
  Bath,
  Square,
  CheckCircle,
  XCircle,
  Globe,
  Mail,
  Phone
} from 'lucide-react'
import { AdWithDetails } from '@/types'

interface CategorySpecificDetailsProps {
  ad: AdWithDetails
}

// Helper function to format field labels
const formatFieldLabel = (key: string): string => {
  return key
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

// Helper function to format field values
const formatFieldValue = (value: any, key: string): string => {
  if (value === null || value === undefined || value === '') return 'Not specified'
  
  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No'
  }
  
  if (Array.isArray(value)) {
    return value.map(v => formatFieldLabel(v)).join(', ')
  }
  
  if (typeof value === 'string') {
    // Handle specific formatting for certain fields
    if (key.includes('size') || key.includes('capacity')) {
      return value.replace(/(\d+)([a-zA-Z]+)/, '$1 $2')
    }
    return formatFieldLabel(value)
  }
  
  return String(value)
}

// Get icon for specific field types
const getFieldIcon = (key: string, categoryName?: string) => {
  const iconClass = "h-4 w-4 text-gray-500"
  
  // Mobile/Electronics icons
  if (key.includes('ram') || key.includes('storage')) return <HardDrive className={iconClass} />
  if (key.includes('camera')) return <Camera className={iconClass} />
  if (key.includes('battery')) return <Battery className={iconClass} />
  if (key.includes('processor') || key.includes('cpu')) return <Cpu className={iconClass} />
  if (key.includes('screen') || key.includes('display')) return <Monitor className={iconClass} />
  
  // Vehicle icons
  if (key.includes('fuel')) return <Fuel className={iconClass} />
  if (key.includes('transmission') || key.includes('gear')) return <Settings className={iconClass} />
  if (key.includes('year') || key.includes('model')) return <CarIcon className={iconClass} />
  if (key.includes('mileage') || key.includes('km')) return <Car className={iconClass} />
  
  // Property icons
  if (key.includes('bedroom')) return <Bed className={iconClass} />
  if (key.includes('bathroom')) return <Bath className={iconClass} />
  if (key.includes('size') || key.includes('area')) return <Square className={iconClass} />
  if (key.includes('furnished')) return <Home className={iconClass} />
  
  // Job icons
  if (key.includes('salary') || key.includes('pay')) return <DollarSign className={iconClass} />
  if (key.includes('experience') || key.includes('level')) return <Users className={iconClass} />
  if (key.includes('location') || key.includes('work')) return <MapPin className={iconClass} />
  if (key.includes('deadline') || key.includes('date')) return <Calendar className={iconClass} />
  if (key.includes('website') || key.includes('url')) return <Globe className={iconClass} />
  if (key.includes('email')) return <Mail className={iconClass} />
  if (key.includes('phone')) return <Phone className={iconClass} />
  
  // Default icon
  return <CheckCircle className={iconClass} />
}

export default function CategorySpecificDetails({ ad }: CategorySpecificDetailsProps) {
  const categoryFields = ad.category_fields || {}
  const mainCategory = ad.main_category || 'sell'
  const categoryName = ad.category?.name || ''
  
  // Combine category fields with main category specific fields
  const allFields: Record<string, any> = { ...categoryFields }
  
  // Add job-specific fields if it's a job posting
  if (mainCategory === 'jobs') {
    if (ad.job_type) allFields.job_type = ad.job_type
    if (ad.salary_range_from || ad.salary_range_to) {
      allFields.salary_range = `Rs ${ad.salary_range_from?.toLocaleString() || '0'} - Rs ${ad.salary_range_to?.toLocaleString() || '0'}`
    }
    if (ad.application_method) allFields.application_method = ad.application_method
    if (ad.application_deadline) allFields.application_deadline = new Date(ad.application_deadline).toLocaleDateString()
    if (ad.employer_name) allFields.employer_name = ad.employer_name
    if (ad.employer_website) allFields.employer_website = ad.employer_website
  }
  
  // Add rental-specific fields if it's a rental
  if (mainCategory === 'rent') {
    if (ad.rental_type) allFields.rental_type = ad.rental_type
    if (ad.availability_from) allFields.availability_from = new Date(ad.availability_from).toLocaleDateString()
    if (ad.availability_to) allFields.availability_to = new Date(ad.availability_to).toLocaleDateString()
  }
  
  // Filter out empty fields
  const displayFields = Object.entries(allFields).filter(([key, value]) => 
    value !== null && value !== undefined && value !== '' && 
    !(Array.isArray(value) && value.length === 0)
  )
  
  if (displayFields.length === 0) {
    return null
  }
  
  // Group fields by category for better organization
  const groupedFields = {
    basic: [] as [string, any][],
    specifications: [] as [string, any][],
    features: [] as [string, any][],
    contact: [] as [string, any][],
    other: [] as [string, any][]
  }
  
  displayFields.forEach(([key, value]) => {
    if (key.includes('brand') || key.includes('model') || key.includes('year') || key.includes('type')) {
      groupedFields.basic.push([key, value])
    } else if (key.includes('ram') || key.includes('storage') || key.includes('engine') || key.includes('fuel') || 
               key.includes('transmission') || key.includes('bedroom') || key.includes('bathroom') || key.includes('size')) {
      groupedFields.specifications.push([key, value])
    } else if (key.includes('features') || key.includes('amenities') || key.includes('benefits') || 
               key.includes('camera') || key.includes('battery') || key.includes('screen')) {
      groupedFields.features.push([key, value])
    } else if (key.includes('employer') || key.includes('website') || key.includes('email') || key.includes('phone')) {
      groupedFields.contact.push([key, value])
    } else {
      groupedFields.other.push([key, value])
    }
  })
  
  const renderFieldGroup = (title: string, fields: [string, any][], icon: React.ReactNode) => {
    if (fields.length === 0) return null
    
    return (
      <div className="mb-6">
        <div className="flex items-center mb-3">
          {icon}
          <h4 className="text-sm font-semibold text-gray-900 ml-2">{title}</h4>
        </div>
        <div className="space-y-2">
          {fields.map(([key, value]) => (
            <div key={key} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
              <div className="flex items-center">
                {getFieldIcon(key, categoryName)}
                <span className="text-gray-600 ml-2 text-sm">{formatFieldLabel(key)}:</span>
              </div>
              <span className="font-medium text-gray-900 text-sm text-right max-w-[60%]">
                {formatFieldValue(value, key)}
              </span>
            </div>
          ))}
        </div>
      </div>
    )
  }
  
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 h-full">
      <div className="flex items-center mb-6">
        {mainCategory === 'jobs' ? (
          <Briefcase className="h-5 w-5 text-purple-600 mr-2" />
        ) : mainCategory === 'rent' ? (
          <Home className="h-5 w-5 text-green-600 mr-2" />
        ) : categoryName.toLowerCase().includes('mobile') ? (
          <Smartphone className="h-5 w-5 text-blue-600 mr-2" />
        ) : categoryName.toLowerCase().includes('car') || categoryName.toLowerCase().includes('vehicle') ? (
          <Car className="h-5 w-5 text-red-600 mr-2" />
        ) : (
          <CheckCircle className="h-5 w-5 text-gray-600 mr-2" />
        )}
        <h3 className="text-lg font-bold font-heading text-gray-900">
          {mainCategory === 'jobs' ? 'Job Details' :
           mainCategory === 'rent' ? 'Property Details' :
           'Product Specifications'}
        </h3>
      </div>
      
      {renderFieldGroup('Basic Information', groupedFields.basic, <CheckCircle className="h-4 w-4 text-blue-600" />)}
      {renderFieldGroup('Specifications', groupedFields.specifications, <Settings className="h-4 w-4 text-green-600" />)}
      {renderFieldGroup('Features & Amenities', groupedFields.features, <CheckCircle className="h-4 w-4 text-purple-600" />)}
      {renderFieldGroup('Contact Information', groupedFields.contact, <Phone className="h-4 w-4 text-orange-600" />)}
      {renderFieldGroup('Additional Details', groupedFields.other, <CheckCircle className="h-4 w-4 text-gray-600" />)}
    </div>
  )
}
