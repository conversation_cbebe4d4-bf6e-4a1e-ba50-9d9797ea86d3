"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ad/[id]/page",{

/***/ "(app-pages-browser)/./src/app/ad/[id]/page.tsx":
/*!**********************************!*\
  !*** ./src/app/ad/[id]/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SingleAdPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Header */ \"(app-pages-browser)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Footer */ \"(app-pages-browser)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _components_ads_AdSlider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ads/AdSlider */ \"(app-pages-browser)/./src/components/ads/AdSlider.tsx\");\n/* harmony import */ var _components_ChatModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ChatModal */ \"(app-pages-browser)/./src/components/ChatModal.tsx\");\n/* harmony import */ var _components_ads_CategorySpecificDetails__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ads/CategorySpecificDetails */ \"(app-pages-browser)/./src/components/ads/CategorySpecificDetails.tsx\");\n/* harmony import */ var _components_ads_JobDetailsCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ads/JobDetailsCard */ \"(app-pages-browser)/./src/components/ads/JobDetailsCard.tsx\");\n/* harmony import */ var _components_ads_RentalDetailsCard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ads/RentalDetailsCard */ \"(app-pages-browser)/./src/components/ads/RentalDetailsCard.tsx\");\n/* harmony import */ var _components_ui_premium__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/premium */ \"(app-pages-browser)/./src/components/ui/premium/index.ts\");\n/* harmony import */ var _lib_services_ads__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/services/ads */ \"(app-pages-browser)/./src/lib/services/ads.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction SingleAdPage() {\n    var _ad_category, _ad_category1, _ad_ad_images_currentImageIndex, _ad_salary_range_from, _ad_salary_range_to, _ad_user_full_name, _ad_user, _ad_user_email, _ad_user1, _ad_user2, _ad_user3, _ad_category2;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_13__.useAuth)();\n    const [ad, setAd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isFavorite, setIsFavorite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [memberAds, setMemberAds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [similarAds, setSimilarAds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [relatedLoading, setRelatedLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChatModalOpen, setIsChatModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check if the current user is the owner of this ad\n    const isOwnAd = user && ad && user.id === ad.user_id;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (params.id) {\n            fetchAd(params.id);\n        }\n    }, [\n        params.id\n    ]);\n    const fetchAd = async (id)=>{\n        try {\n            setLoading(true);\n            const adData = await _lib_services_ads__WEBPACK_IMPORTED_MODULE_11__.AdService.getAdById(id);\n            if (!adData) {\n                setError(\"Ad not found\");\n                return;\n            }\n            setAd(adData);\n            // Fetch related ads after getting the main ad\n            await fetchRelatedAds(adData);\n        } catch (error) {\n            console.error(\"Error fetching ad:\", error);\n            setError(\"Failed to load ad\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchRelatedAds = async (currentAd)=>{\n        try {\n            setRelatedLoading(true);\n            // Fetch more ads from the same member (excluding current ad)\n            const memberAdsResult = await _lib_services_ads__WEBPACK_IMPORTED_MODULE_11__.AdService.getAds({\n                userId: currentAd.user_id,\n                status: \"active\"\n            }, 1, 8);\n            const filteredMemberAds = memberAdsResult.ads.filter((memberAd)=>memberAd.id !== currentAd.id);\n            setMemberAds(filteredMemberAds);\n            // Fetch similar ads from the same subcategory (excluding current ad and member ads)\n            const similarAdsResult = await _lib_services_ads__WEBPACK_IMPORTED_MODULE_11__.AdService.getAds({\n                subcategory_id: currentAd.subcategory_id,\n                status: \"active\"\n            }, 1, 8);\n            const filteredSimilarAds = similarAdsResult.ads.filter((similarAd)=>similarAd.id !== currentAd.id && similarAd.user_id !== currentAd.user_id);\n            setSimilarAds(filteredSimilarAds);\n        } catch (error) {\n            console.error(\"Error fetching related ads:\", error);\n        } finally{\n            setRelatedLoading(false);\n        }\n    };\n    const handlePrevImage = ()=>{\n        if ((ad === null || ad === void 0 ? void 0 : ad.ad_images) && ad.ad_images.length > 0) {\n            setCurrentImageIndex((prev)=>prev === 0 ? ad.ad_images.length - 1 : prev - 1);\n        }\n    };\n    const handleNextImage = ()=>{\n        if ((ad === null || ad === void 0 ? void 0 : ad.ad_images) && ad.ad_images.length > 0) {\n            setCurrentImageIndex((prev)=>prev === ad.ad_images.length - 1 ? 0 : prev + 1);\n        }\n    };\n    const handleShare = async ()=>{\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title: ad === null || ad === void 0 ? void 0 : ad.title,\n                    text: ad === null || ad === void 0 ? void 0 : ad.description,\n                    url: window.location.href\n                });\n            } catch (error) {\n                console.log(\"Error sharing:\", error);\n            }\n        } else {\n            // Fallback: copy to clipboard\n            try {\n                await navigator.clipboard.writeText(window.location.href);\n                // Use premium toast notification instead of alert\n                const { showAlert } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_ui_ConfirmationDialog_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.tsx\"));\n                await showAlert({\n                    title: \"Success\",\n                    message: \"Link copied to clipboard!\",\n                    variant: \"success\"\n                });\n            } catch (error) {\n                console.error(\"Failed to copy link:\", error);\n            }\n        }\n    };\n    const toggleFavorite = ()=>{\n        setIsFavorite(!isFavorite);\n    // TODO: Implement favorite functionality\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-8 bg-gray-300 rounded w-1/4 mb-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-96 bg-gray-300 rounded-xl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 bg-gray-300 rounded w-3/4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-gray-300 rounded w-1/2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-20 bg-gray-300 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !ad) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                children: error || \"Ad not found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.back(),\n                                className: \"text-primary-blue hover:text-primary-blue/80\",\n                                children: \"Go back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n            lineNumber: 185,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex items-center space-x-2 text-sm text-gray-500 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/\",\n                                className: \"hover:text-primary-blue transition-colors font-medium\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"›\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/ads\",\n                                className: \"hover:text-primary-blue transition-colors font-medium\",\n                                children: \"All Ads\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"›\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/category/\".concat((_ad_category = ad.category) === null || _ad_category === void 0 ? void 0 : _ad_category.slug),\n                                className: \"hover:text-primary-blue transition-colors font-medium\",\n                                children: (_ad_category1 = ad.category) === null || _ad_category1 === void 0 ? void 0 : _ad_category1.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"›\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-900 truncate font-semibold\",\n                                children: ad.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl md:text-3xl font-bold font-heading text-gray-900 mb-3\",\n                                children: ad.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: ad.location\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.formatDate)(ad.created_at)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this),\n                                    isOwnAd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    ad.view_count || 0,\n                                                    \" views\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShare,\n                                        className: \"flex items-center px-4 py-2 bg-white text-gray-600 hover:bg-gray-50 border border-gray-300 rounded-lg transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Share\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleFavorite,\n                                        className: \"flex items-center px-4 py-2 rounded-lg transition-colors duration-200 \".concat(isFavorite ? \"bg-red-500 text-white hover:bg-red-600\" : \"bg-white text-gray-600 hover:bg-red-50 hover:text-red-600 border border-gray-300\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2 \".concat(isFavorite ? \"fill-current\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Save ad\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8 mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_10__.PremiumCard, {\n                                        variant: \"premium\",\n                                        padding: \"none\",\n                                        className: \"mb-4 overflow-hidden\",\n                                        children: ad.ad_images && ad.ad_images.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-[400px] md:h-[500px] bg-gray-100\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: (_ad_ad_images_currentImageIndex = ad.ad_images[currentImageIndex]) === null || _ad_ad_images_currentImageIndex === void 0 ? void 0 : _ad_ad_images_currentImageIndex.image_url,\n                                                        alt: ad.title,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    ad.ad_images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 right-4 bg-black/80 backdrop-blur-sm text-white px-3 py-2 rounded-lg text-sm font-medium\",\n                                                        children: [\n                                                            currentImageIndex + 1,\n                                                            \" / \",\n                                                            ad.ad_images.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    ad.ad_images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handlePrevImage,\n                                                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm text-gray-800 p-3 rounded-lg shadow-lg hover:bg-white transition-colors duration-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleNextImage,\n                                                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm text-gray-800 p-3 rounded-lg shadow-lg hover:bg-white transition-colors duration-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-[400px] md:h-[500px] bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl md:text-6xl mb-4\",\n                                                        children: \"\\uD83D\\uDCF7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-base md:text-lg font-medium\",\n                                                        children: \"No images available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Contact seller for more photos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 13\n                                    }, this),\n                                    ad.ad_images && ad.ad_images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-3 overflow-x-auto pb-2 custom-scrollbar\",\n                                        children: ad.ad_images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setCurrentImageIndex(index),\n                                                className: \"flex-shrink-0 w-20 h-20 overflow-hidden border-2 rounded-lg transition-all duration-200 \".concat(index === currentImageIndex ? \"border-primary-blue shadow-md\" : \"border-gray-200 hover:border-primary-blue/50\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: image.image_url,\n                                                    alt: \"\".concat(ad.title, \" \").concat(index + 1),\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sticky top-24 space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white border border-gray-200 rounded-lg p-4 shadow-sm\",\n                                            children: ad.main_category !== \"jobs\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-600 mb-2\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.formatCurrency)(ad.price)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2 mb-3\",\n                                                        children: [\n                                                            ad.negotiable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Negotiable\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            ad.main_category === \"rent\" && ad.rental_type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\",\n                                                                children: [\n                                                                    \"per \",\n                                                                    ad.rental_type === \"daily\" ? \"day\" : ad.rental_type === \"monthly\" ? \"month\" : \"year\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-bold text-purple-600 mb-2\",\n                                                        children: ad.employer_name || \"Job Opportunity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    (ad.salary_range_from || ad.salary_range_to) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-base font-semibold text-green-600 mb-2\",\n                                                        children: [\n                                                            \"Rs \",\n                                                            ((_ad_salary_range_from = ad.salary_range_from) === null || _ad_salary_range_from === void 0 ? void 0 : _ad_salary_range_from.toLocaleString()) || \"0\",\n                                                            \" - Rs \",\n                                                            ((_ad_salary_range_to = ad.salary_range_to) === null || _ad_salary_range_to === void 0 ? void 0 : _ad_salary_range_to.toLocaleString()) || \"0\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs font-normal text-gray-500\",\n                                                                children: \"per month\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white border border-gray-200 rounded-lg p-4 shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-base font-bold text-gray-900 mb-3\",\n                                                    children: \"Contact Seller\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-gradient-to-br from-primary-blue to-secondary-blue rounded-full flex items-center justify-center text-white font-bold text-sm mr-3\",\n                                                            children: ((_ad_user = ad.user) === null || _ad_user === void 0 ? void 0 : (_ad_user_full_name = _ad_user.full_name) === null || _ad_user_full_name === void 0 ? void 0 : _ad_user_full_name.charAt(0)) || ((_ad_user1 = ad.user) === null || _ad_user1 === void 0 ? void 0 : (_ad_user_email = _ad_user1.email) === null || _ad_user_email === void 0 ? void 0 : _ad_user_email.charAt(0)) || \"U\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-semibold text-gray-900 text-sm\",\n                                                                    children: ((_ad_user2 = ad.user) === null || _ad_user2 === void 0 ? void 0 : _ad_user2.full_name) || \"Anonymous\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"Member since \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.formatDate)(((_ad_user3 = ad.user) === null || _ad_user3 === void 0 ? void 0 : _ad_user3.created_at) || ad.created_at)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: user && !isOwnAd ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setIsChatModalOpen(true),\n                                                                className: \"w-full flex items-center justify-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors duration-200 text-sm font-medium\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Send Message\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            ad.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>window.open(\"tel:\".concat(ad.phone)),\n                                                                className: \"w-full flex items-center justify-center px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200 text-sm font-medium\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Call Now\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true) : isOwnAd ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-3 bg-green-50 rounded-lg text-green-800\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs\",\n                                                            children: \"This is your ad\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-3 bg-blue-50 rounded-lg text-blue-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs mb-2\",\n                                                                children: \"Sign in to contact the seller\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/auth/signin\"),\n                                                                className: \"px-3 py-1 bg-primary-blue text-white rounded text-xs font-medium hover:bg-primary-blue/90 transition-colors duration-200\",\n                                                                children: \"Sign In\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-base font-bold text-yellow-800 mb-3 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Stay Alert\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 text-xs text-yellow-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• Never share card details or OTPs, and avoid making payments through links sent to you.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• Verify items before purchase before making any payment.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• OKDOI does not offer a delivery service.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• Stay safe!\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border border-gray-200 rounded-lg p-6 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold font-heading text-gray-900 mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2 text-primary-blue\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Basic Information\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                                        children: [\n                                            ad.main_category !== \"jobs\" && ad.condition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 font-medium text-sm mb-1\",\n                                                        children: \"Condition\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-gray-900 capitalize text-sm\",\n                                                        children: ad.condition\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 font-medium text-sm mb-1\",\n                                                        children: \"Category\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-primary-blue text-sm\",\n                                                        children: (_ad_category2 = ad.category) === null || _ad_category2 === void 0 ? void 0 : _ad_category2.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 15\n                                            }, this),\n                                            ad.subcategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 font-medium text-sm mb-1\",\n                                                        children: \"Subcategory\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-secondary-blue text-sm\",\n                                                        children: ad.subcategory.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 font-medium text-sm mb-1\",\n                                                        children: \"Type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-gray-900 text-sm\",\n                                                        children: ad.main_category === \"jobs\" ? \"Job Posting\" : ad.main_category === \"rent\" ? \"Property Rental\" : \"For Sale\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 15\n                                            }, this),\n                                            ad.main_category !== \"jobs\" && ad.negotiable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 font-medium text-sm mb-1\",\n                                                        children: \"Negotiable\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-green-600 text-sm flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Yes\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border border-gray-200 rounded-lg shadow-sm\",\n                                children: ad.main_category === \"jobs\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_JobDetailsCard__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    ad: ad\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 15\n                                }, this) : ad.main_category === \"rent\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_RentalDetailsCard__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    ad: ad\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_CategorySpecificDetails__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    ad: ad\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 516,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border border-gray-200 rounded-lg p-6 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-bold font-heading text-gray-900 mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2 text-primary-blue\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Description\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose prose-lg max-w-none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 leading-relaxed whitespace-pre-wrap\",\n                                            children: ad.description || \"No description provided.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                        lineNumber: 469,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-16 space-y-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_AdSlider__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    ads: memberAds,\n                                    title: \"More ads from this member\",\n                                    loading: relatedLoading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 543,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_AdSlider__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    ads: similarAds,\n                                    title: \"Similar ads\",\n                                    loading: relatedLoading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                        lineNumber: 541,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                lineNumber: 562,\n                columnNumber: 7\n            }, this),\n            !isOwnAd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isChatModalOpen,\n                onClose: ()=>setIsChatModalOpen(false),\n                ad: ad\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                lineNumber: 566,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n        lineNumber: 206,\n        columnNumber: 5\n    }, this);\n}\n_s(SingleAdPage, \"o6poQkHByEasBXYjMH2NOZguUbA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_13__.useAuth\n    ];\n});\n_c = SingleAdPage;\nvar _c;\n$RefreshReg$(_c, \"SingleAdPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ad/[id]/page.tsx\n"));

/***/ })

});