"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ad/[id]/page",{

/***/ "(app-pages-browser)/./src/components/ads/CategorySpecificDetails.tsx":
/*!********************************************************!*\
  !*** ./src/components/ads/CategorySpecificDetails.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CategorySpecificDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/battery.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/fuel.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bed.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bath.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n// Helper function to format field labels\nconst formatFieldLabel = (key)=>{\n    return key.split(\"_\").map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(\" \");\n};\n// Helper function to format field values\nconst formatFieldValue = (value, key)=>{\n    if (value === null || value === undefined || value === \"\") return \"Not specified\";\n    if (typeof value === \"boolean\") {\n        return value ? \"Yes\" : \"No\";\n    }\n    if (Array.isArray(value)) {\n        return value.map((v)=>formatFieldLabel(v)).join(\", \");\n    }\n    if (typeof value === \"string\") {\n        // Handle specific formatting for certain fields\n        if (key.includes(\"size\") || key.includes(\"capacity\")) {\n            return value.replace(/(\\d+)([a-zA-Z]+)/, \"$1 $2\");\n        }\n        return formatFieldLabel(value);\n    }\n    return String(value);\n};\n// Get icon for specific field types\nconst getFieldIcon = (key, categoryName)=>{\n    const iconClass = \"h-4 w-4 text-gray-500\";\n    // Mobile/Electronics icons\n    if (key.includes(\"ram\") || key.includes(\"storage\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 74,\n        columnNumber: 62\n    }, undefined);\n    if (key.includes(\"camera\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 75,\n        columnNumber: 38\n    }, undefined);\n    if (key.includes(\"battery\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 76,\n        columnNumber: 39\n    }, undefined);\n    if (key.includes(\"processor\") || key.includes(\"cpu\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 77,\n        columnNumber: 64\n    }, undefined);\n    if (key.includes(\"screen\") || key.includes(\"display\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 78,\n        columnNumber: 65\n    }, undefined);\n    // Vehicle icons\n    if (key.includes(\"fuel\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 81,\n        columnNumber: 36\n    }, undefined);\n    if (key.includes(\"transmission\") || key.includes(\"gear\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 82,\n        columnNumber: 68\n    }, undefined);\n    if (key.includes(\"year\") || key.includes(\"model\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 83,\n        columnNumber: 61\n    }, undefined);\n    if (key.includes(\"mileage\") || key.includes(\"km\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 84,\n        columnNumber: 61\n    }, undefined);\n    // Property icons\n    if (key.includes(\"bedroom\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 87,\n        columnNumber: 39\n    }, undefined);\n    if (key.includes(\"bathroom\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 88,\n        columnNumber: 40\n    }, undefined);\n    if (key.includes(\"size\") || key.includes(\"area\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 89,\n        columnNumber: 60\n    }, undefined);\n    if (key.includes(\"furnished\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 90,\n        columnNumber: 41\n    }, undefined);\n    // Job icons\n    if (key.includes(\"salary\") || key.includes(\"pay\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 93,\n        columnNumber: 61\n    }, undefined);\n    if (key.includes(\"experience\") || key.includes(\"level\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 94,\n        columnNumber: 67\n    }, undefined);\n    if (key.includes(\"location\") || key.includes(\"work\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 95,\n        columnNumber: 64\n    }, undefined);\n    if (key.includes(\"deadline\") || key.includes(\"date\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 96,\n        columnNumber: 64\n    }, undefined);\n    if (key.includes(\"website\") || key.includes(\"url\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 97,\n        columnNumber: 62\n    }, undefined);\n    if (key.includes(\"email\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 98,\n        columnNumber: 37\n    }, undefined);\n    if (key.includes(\"phone\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 99,\n        columnNumber: 37\n    }, undefined);\n    // Default icon\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 102,\n        columnNumber: 10\n    }, undefined);\n};\nfunction CategorySpecificDetails(param) {\n    let { ad } = param;\n    var _ad_category;\n    const categoryFields = ad.category_fields || {};\n    const mainCategory = ad.main_category || \"sell\";\n    const categoryName = ((_ad_category = ad.category) === null || _ad_category === void 0 ? void 0 : _ad_category.name) || \"\";\n    // Combine category fields with main category specific fields\n    const allFields = {\n        ...categoryFields\n    };\n    // Add job-specific fields if it's a job posting\n    if (mainCategory === \"jobs\") {\n        if (ad.job_type) allFields.job_type = ad.job_type;\n        if (ad.salary_range_from || ad.salary_range_to) {\n            var _ad_salary_range_from, _ad_salary_range_to;\n            allFields.salary_range = \"Rs \".concat(((_ad_salary_range_from = ad.salary_range_from) === null || _ad_salary_range_from === void 0 ? void 0 : _ad_salary_range_from.toLocaleString()) || \"0\", \" - Rs \").concat(((_ad_salary_range_to = ad.salary_range_to) === null || _ad_salary_range_to === void 0 ? void 0 : _ad_salary_range_to.toLocaleString()) || \"0\");\n        }\n        if (ad.application_method) allFields.application_method = ad.application_method;\n        if (ad.application_deadline) allFields.application_deadline = new Date(ad.application_deadline).toLocaleDateString();\n        if (ad.employer_name) allFields.employer_name = ad.employer_name;\n        if (ad.employer_website) allFields.employer_website = ad.employer_website;\n    }\n    // Add rental-specific fields if it's a rental\n    if (mainCategory === \"rent\") {\n        if (ad.rental_type) allFields.rental_type = ad.rental_type;\n        if (ad.availability_from) allFields.availability_from = new Date(ad.availability_from).toLocaleDateString();\n        if (ad.availability_to) allFields.availability_to = new Date(ad.availability_to).toLocaleDateString();\n    }\n    // Filter out empty fields\n    const displayFields = Object.entries(allFields).filter((param)=>{\n        let [key, value] = param;\n        return value !== null && value !== undefined && value !== \"\" && !(Array.isArray(value) && value.length === 0);\n    });\n    if (displayFields.length === 0) {\n        return null;\n    }\n    // Group fields by category for better organization\n    const groupedFields = {\n        basic: [],\n        specifications: [],\n        features: [],\n        contact: [],\n        other: []\n    };\n    displayFields.forEach((param)=>{\n        let [key, value] = param;\n        if (key.includes(\"brand\") || key.includes(\"model\") || key.includes(\"year\") || key.includes(\"type\")) {\n            groupedFields.basic.push([\n                key,\n                value\n            ]);\n        } else if (key.includes(\"ram\") || key.includes(\"storage\") || key.includes(\"engine\") || key.includes(\"fuel\") || key.includes(\"transmission\") || key.includes(\"bedroom\") || key.includes(\"bathroom\") || key.includes(\"size\")) {\n            groupedFields.specifications.push([\n                key,\n                value\n            ]);\n        } else if (key.includes(\"features\") || key.includes(\"amenities\") || key.includes(\"benefits\") || key.includes(\"camera\") || key.includes(\"battery\") || key.includes(\"screen\")) {\n            groupedFields.features.push([\n                key,\n                value\n            ]);\n        } else if (key.includes(\"employer\") || key.includes(\"website\") || key.includes(\"email\") || key.includes(\"phone\")) {\n            groupedFields.contact.push([\n                key,\n                value\n            ]);\n        } else {\n            groupedFields.other.push([\n                key,\n                value\n            ]);\n        }\n    });\n    const renderFieldGroup = (title, fields, icon)=>{\n        if (fields.length === 0) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center mb-3\",\n                    children: [\n                        icon,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-semibold text-gray-900 ml-2\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: fields.map((param)=>{\n                        let [key, value] = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-1\",\n                                    children: [\n                                        getFieldIcon(key, categoryName),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 ml-1 text-sm font-medium\",\n                                            children: formatFieldLabel(key)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-semibold text-gray-900 text-sm\",\n                                    children: formatFieldValue(value, key)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, key, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n            lineNumber: 171,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white border border-gray-200 rounded-lg p-6 h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-6\",\n                children: [\n                    mainCategory === \"jobs\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        className: \"h-5 w-5 text-purple-600 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this) : mainCategory === \"rent\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-5 w-5 text-green-600 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this) : categoryName.toLowerCase().includes(\"mobile\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        className: \"h-5 w-5 text-blue-600 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this) : categoryName.toLowerCase().includes(\"car\") || categoryName.toLowerCase().includes(\"vehicle\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-5 w-5 text-red-600 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        className: \"h-5 w-5 text-gray-600 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-bold font-heading text-gray-900\",\n                        children: mainCategory === \"jobs\" ? \"Job Details\" : mainCategory === \"rent\" ? \"Property Details\" : \"Product Specifications\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            renderFieldGroup(\"Basic Information\", groupedFields.basic, /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                className: \"h-4 w-4 text-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                lineNumber: 214,\n                columnNumber: 67\n            }, this)),\n            renderFieldGroup(\"Specifications\", groupedFields.specifications, /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-4 w-4 text-green-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                lineNumber: 215,\n                columnNumber: 73\n            }, this)),\n            renderFieldGroup(\"Features & Amenities\", groupedFields.features, /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                className: \"h-4 w-4 text-purple-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                lineNumber: 216,\n                columnNumber: 73\n            }, this)),\n            renderFieldGroup(\"Contact Information\", groupedFields.contact, /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                className: \"h-4 w-4 text-orange-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                lineNumber: 217,\n                columnNumber: 71\n            }, this)),\n            renderFieldGroup(\"Additional Details\", groupedFields.other, /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                className: \"h-4 w-4 text-gray-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                lineNumber: 218,\n                columnNumber: 68\n            }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, this);\n}\n_c = CategorySpecificDetails;\nvar _c;\n$RefreshReg$(_c, \"CategorySpecificDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ads/CategorySpecificDetails.tsx\n"));

/***/ })

});