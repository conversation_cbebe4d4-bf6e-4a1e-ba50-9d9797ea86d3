"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ad/[id]/page",{

/***/ "(app-pages-browser)/./src/components/ads/CategorySpecificDetails.tsx":
/*!********************************************************!*\
  !*** ./src/components/ads/CategorySpecificDetails.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CategorySpecificDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/battery.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/fuel.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bed.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bath.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Battery,Bed,Briefcase,Calendar,Camera,Car,CheckCircle,Cpu,DollarSign,Fuel,Globe,HardDrive,Home,Mail,MapPin,Monitor,Phone,Settings,Smartphone,Square,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n// Helper function to format field labels\nconst formatFieldLabel = (key)=>{\n    return key.split(\"_\").map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(\" \");\n};\n// Helper function to format field values\nconst formatFieldValue = (value, key)=>{\n    if (value === null || value === undefined || value === \"\") return \"Not specified\";\n    if (typeof value === \"boolean\") {\n        return value ? \"Yes\" : \"No\";\n    }\n    if (Array.isArray(value)) {\n        return value.map((v)=>formatFieldLabel(v)).join(\", \");\n    }\n    if (typeof value === \"string\") {\n        // Handle specific formatting for certain fields\n        if (key.includes(\"size\") || key.includes(\"capacity\")) {\n            return value.replace(/(\\d+)([a-zA-Z]+)/, \"$1 $2\");\n        }\n        return formatFieldLabel(value);\n    }\n    return String(value);\n};\n// Get icon for specific field types\nconst getFieldIcon = (key, categoryName)=>{\n    const iconClass = \"h-4 w-4 text-gray-500\";\n    // Mobile/Electronics icons\n    if (key.includes(\"ram\") || key.includes(\"storage\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 74,\n        columnNumber: 62\n    }, undefined);\n    if (key.includes(\"camera\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 75,\n        columnNumber: 38\n    }, undefined);\n    if (key.includes(\"battery\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 76,\n        columnNumber: 39\n    }, undefined);\n    if (key.includes(\"processor\") || key.includes(\"cpu\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 77,\n        columnNumber: 64\n    }, undefined);\n    if (key.includes(\"screen\") || key.includes(\"display\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 78,\n        columnNumber: 65\n    }, undefined);\n    // Vehicle icons\n    if (key.includes(\"fuel\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 81,\n        columnNumber: 36\n    }, undefined);\n    if (key.includes(\"transmission\") || key.includes(\"gear\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 82,\n        columnNumber: 68\n    }, undefined);\n    if (key.includes(\"year\") || key.includes(\"model\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 83,\n        columnNumber: 61\n    }, undefined);\n    if (key.includes(\"mileage\") || key.includes(\"km\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 84,\n        columnNumber: 61\n    }, undefined);\n    // Property icons\n    if (key.includes(\"bedroom\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 87,\n        columnNumber: 39\n    }, undefined);\n    if (key.includes(\"bathroom\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 88,\n        columnNumber: 40\n    }, undefined);\n    if (key.includes(\"size\") || key.includes(\"area\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 89,\n        columnNumber: 60\n    }, undefined);\n    if (key.includes(\"furnished\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 90,\n        columnNumber: 41\n    }, undefined);\n    // Job icons\n    if (key.includes(\"salary\") || key.includes(\"pay\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 93,\n        columnNumber: 61\n    }, undefined);\n    if (key.includes(\"experience\") || key.includes(\"level\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 94,\n        columnNumber: 67\n    }, undefined);\n    if (key.includes(\"location\") || key.includes(\"work\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 95,\n        columnNumber: 64\n    }, undefined);\n    if (key.includes(\"deadline\") || key.includes(\"date\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 96,\n        columnNumber: 64\n    }, undefined);\n    if (key.includes(\"website\") || key.includes(\"url\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 97,\n        columnNumber: 62\n    }, undefined);\n    if (key.includes(\"email\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 98,\n        columnNumber: 37\n    }, undefined);\n    if (key.includes(\"phone\")) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 99,\n        columnNumber: 37\n    }, undefined);\n    // Default icon\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n        className: iconClass\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 102,\n        columnNumber: 10\n    }, undefined);\n};\nfunction CategorySpecificDetails(param) {\n    let { ad } = param;\n    var _ad_category;\n    const categoryFields = ad.category_fields || {};\n    const mainCategory = ad.main_category || \"sell\";\n    const categoryName = ((_ad_category = ad.category) === null || _ad_category === void 0 ? void 0 : _ad_category.name) || \"\";\n    // Combine category fields with main category specific fields\n    const allFields = {\n        ...categoryFields\n    };\n    // Add job-specific fields if it's a job posting\n    if (mainCategory === \"jobs\") {\n        if (ad.job_type) allFields.job_type = ad.job_type;\n        if (ad.salary_range_from || ad.salary_range_to) {\n            var _ad_salary_range_from, _ad_salary_range_to;\n            allFields.salary_range = \"Rs \".concat(((_ad_salary_range_from = ad.salary_range_from) === null || _ad_salary_range_from === void 0 ? void 0 : _ad_salary_range_from.toLocaleString()) || \"0\", \" - Rs \").concat(((_ad_salary_range_to = ad.salary_range_to) === null || _ad_salary_range_to === void 0 ? void 0 : _ad_salary_range_to.toLocaleString()) || \"0\");\n        }\n        if (ad.application_method) allFields.application_method = ad.application_method;\n        if (ad.application_deadline) allFields.application_deadline = new Date(ad.application_deadline).toLocaleDateString();\n        if (ad.employer_name) allFields.employer_name = ad.employer_name;\n        if (ad.employer_website) allFields.employer_website = ad.employer_website;\n    }\n    // Add rental-specific fields if it's a rental\n    if (mainCategory === \"rent\") {\n        if (ad.rental_type) allFields.rental_type = ad.rental_type;\n        if (ad.availability_from) allFields.availability_from = new Date(ad.availability_from).toLocaleDateString();\n        if (ad.availability_to) allFields.availability_to = new Date(ad.availability_to).toLocaleDateString();\n    }\n    // Filter out empty fields\n    const displayFields = Object.entries(allFields).filter((param)=>{\n        let [key, value] = param;\n        return value !== null && value !== undefined && value !== \"\" && !(Array.isArray(value) && value.length === 0);\n    });\n    if (displayFields.length === 0) {\n        return null;\n    }\n    // Group fields by category for better organization\n    const groupedFields = {\n        basic: [],\n        specifications: [],\n        features: [],\n        contact: [],\n        other: []\n    };\n    displayFields.forEach((param)=>{\n        let [key, value] = param;\n        if (key.includes(\"brand\") || key.includes(\"model\") || key.includes(\"year\") || key.includes(\"type\")) {\n            groupedFields.basic.push([\n                key,\n                value\n            ]);\n        } else if (key.includes(\"ram\") || key.includes(\"storage\") || key.includes(\"engine\") || key.includes(\"fuel\") || key.includes(\"transmission\") || key.includes(\"bedroom\") || key.includes(\"bathroom\") || key.includes(\"size\")) {\n            groupedFields.specifications.push([\n                key,\n                value\n            ]);\n        } else if (key.includes(\"features\") || key.includes(\"amenities\") || key.includes(\"benefits\") || key.includes(\"camera\") || key.includes(\"battery\") || key.includes(\"screen\")) {\n            groupedFields.features.push([\n                key,\n                value\n            ]);\n        } else if (key.includes(\"employer\") || key.includes(\"website\") || key.includes(\"email\") || key.includes(\"phone\")) {\n            groupedFields.contact.push([\n                key,\n                value\n            ]);\n        } else {\n            groupedFields.other.push([\n                key,\n                value\n            ]);\n        }\n    });\n    const renderFieldGroup = (title, fields, icon)=>{\n        if (fields.length === 0) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center mb-3\",\n                    children: [\n                        icon,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-semibold text-gray-900 ml-2\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: fields.map((param)=>{\n                        let [key, value] = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        getFieldIcon(key, categoryName),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 ml-2 text-sm\",\n                                            children: [\n                                                formatFieldLabel(key),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-gray-900 text-sm text-right max-w-[60%]\",\n                                    children: formatFieldValue(value, key)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, key, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n            lineNumber: 171,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white border border-gray-200 rounded-lg p-6 h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-6\",\n                children: [\n                    mainCategory === \"jobs\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        className: \"h-5 w-5 text-purple-600 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this) : mainCategory === \"rent\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-5 w-5 text-green-600 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this) : categoryName.toLowerCase().includes(\"mobile\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        className: \"h-5 w-5 text-blue-600 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this) : categoryName.toLowerCase().includes(\"car\") || categoryName.toLowerCase().includes(\"vehicle\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-5 w-5 text-red-600 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        className: \"h-5 w-5 text-gray-600 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-bold font-heading text-gray-900\",\n                        children: mainCategory === \"jobs\" ? \"Job Details\" : mainCategory === \"rent\" ? \"Property Details\" : \"Product Specifications\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            renderFieldGroup(\"Basic Information\", groupedFields.basic, /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                className: \"h-4 w-4 text-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                lineNumber: 214,\n                columnNumber: 67\n            }, this)),\n            renderFieldGroup(\"Specifications\", groupedFields.specifications, /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-4 w-4 text-green-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                lineNumber: 215,\n                columnNumber: 73\n            }, this)),\n            renderFieldGroup(\"Features & Amenities\", groupedFields.features, /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                className: \"h-4 w-4 text-purple-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                lineNumber: 216,\n                columnNumber: 73\n            }, this)),\n            renderFieldGroup(\"Contact Information\", groupedFields.contact, /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                className: \"h-4 w-4 text-orange-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                lineNumber: 217,\n                columnNumber: 71\n            }, this)),\n            renderFieldGroup(\"Additional Details\", groupedFields.other, /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Battery_Bed_Briefcase_Calendar_Camera_Car_CheckCircle_Cpu_DollarSign_Fuel_Globe_HardDrive_Home_Mail_MapPin_Monitor_Phone_Settings_Smartphone_Square_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                className: \"h-4 w-4 text-gray-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n                lineNumber: 218,\n                columnNumber: 68\n            }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\ads\\\\CategorySpecificDetails.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, this);\n}\n_c = CategorySpecificDetails;\nvar _c;\n$RefreshReg$(_c, \"CategorySpecificDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ads/CategorySpecificDetails.tsx\n"));

/***/ })

});