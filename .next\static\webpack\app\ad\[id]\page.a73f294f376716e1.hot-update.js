"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ad/[id]/page",{

/***/ "(app-pages-browser)/./src/app/ad/[id]/page.tsx":
/*!**********************************!*\
  !*** ./src/app/ad/[id]/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SingleAdPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronLeft,ChevronRight,Eye,Globe,Heart,MapPin,MessageCircle,Phone,Share2,Shield,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Header */ \"(app-pages-browser)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Footer */ \"(app-pages-browser)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _components_ads_AdSlider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ads/AdSlider */ \"(app-pages-browser)/./src/components/ads/AdSlider.tsx\");\n/* harmony import */ var _components_ChatModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ChatModal */ \"(app-pages-browser)/./src/components/ChatModal.tsx\");\n/* harmony import */ var _components_ads_CategorySpecificDetails__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ads/CategorySpecificDetails */ \"(app-pages-browser)/./src/components/ads/CategorySpecificDetails.tsx\");\n/* harmony import */ var _components_ads_JobDetailsCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ads/JobDetailsCard */ \"(app-pages-browser)/./src/components/ads/JobDetailsCard.tsx\");\n/* harmony import */ var _components_ads_RentalDetailsCard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ads/RentalDetailsCard */ \"(app-pages-browser)/./src/components/ads/RentalDetailsCard.tsx\");\n/* harmony import */ var _components_ui_premium__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/premium */ \"(app-pages-browser)/./src/components/ui/premium/index.ts\");\n/* harmony import */ var _lib_services_ads__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/services/ads */ \"(app-pages-browser)/./src/lib/services/ads.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction SingleAdPage() {\n    var _ad_category, _ad_category1, _ad_ad_images_currentImageIndex, _ad_salary_range_from, _ad_salary_range_to, _ad_user_full_name, _ad_user, _ad_user_email, _ad_user1, _ad_user2, _ad_user3, _ad_category2;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_13__.useAuth)();\n    const [ad, setAd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isFavorite, setIsFavorite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [memberAds, setMemberAds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [similarAds, setSimilarAds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [relatedLoading, setRelatedLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChatModalOpen, setIsChatModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check if the current user is the owner of this ad\n    const isOwnAd = user && ad && user.id === ad.user_id;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (params.id) {\n            fetchAd(params.id);\n        }\n    }, [\n        params.id\n    ]);\n    const fetchAd = async (id)=>{\n        try {\n            setLoading(true);\n            const adData = await _lib_services_ads__WEBPACK_IMPORTED_MODULE_11__.AdService.getAdById(id);\n            if (!adData) {\n                setError(\"Ad not found\");\n                return;\n            }\n            setAd(adData);\n            // Fetch related ads after getting the main ad\n            await fetchRelatedAds(adData);\n        } catch (error) {\n            console.error(\"Error fetching ad:\", error);\n            setError(\"Failed to load ad\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchRelatedAds = async (currentAd)=>{\n        try {\n            setRelatedLoading(true);\n            // Fetch more ads from the same member (excluding current ad)\n            const memberAdsResult = await _lib_services_ads__WEBPACK_IMPORTED_MODULE_11__.AdService.getAds({\n                userId: currentAd.user_id,\n                status: \"active\"\n            }, 1, 8);\n            const filteredMemberAds = memberAdsResult.ads.filter((memberAd)=>memberAd.id !== currentAd.id);\n            setMemberAds(filteredMemberAds);\n            // Fetch similar ads from the same subcategory (excluding current ad and member ads)\n            const similarAdsResult = await _lib_services_ads__WEBPACK_IMPORTED_MODULE_11__.AdService.getAds({\n                subcategory_id: currentAd.subcategory_id,\n                status: \"active\"\n            }, 1, 8);\n            const filteredSimilarAds = similarAdsResult.ads.filter((similarAd)=>similarAd.id !== currentAd.id && similarAd.user_id !== currentAd.user_id);\n            setSimilarAds(filteredSimilarAds);\n        } catch (error) {\n            console.error(\"Error fetching related ads:\", error);\n        } finally{\n            setRelatedLoading(false);\n        }\n    };\n    const handlePrevImage = ()=>{\n        if ((ad === null || ad === void 0 ? void 0 : ad.ad_images) && ad.ad_images.length > 0) {\n            setCurrentImageIndex((prev)=>prev === 0 ? ad.ad_images.length - 1 : prev - 1);\n        }\n    };\n    const handleNextImage = ()=>{\n        if ((ad === null || ad === void 0 ? void 0 : ad.ad_images) && ad.ad_images.length > 0) {\n            setCurrentImageIndex((prev)=>prev === ad.ad_images.length - 1 ? 0 : prev + 1);\n        }\n    };\n    const handleShare = async ()=>{\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title: ad === null || ad === void 0 ? void 0 : ad.title,\n                    text: ad === null || ad === void 0 ? void 0 : ad.description,\n                    url: window.location.href\n                });\n            } catch (error) {\n                console.log(\"Error sharing:\", error);\n            }\n        } else {\n            // Fallback: copy to clipboard\n            try {\n                await navigator.clipboard.writeText(window.location.href);\n                // Use premium toast notification instead of alert\n                const { showAlert } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_ui_ConfirmationDialog_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.tsx\"));\n                await showAlert({\n                    title: \"Success\",\n                    message: \"Link copied to clipboard!\",\n                    variant: \"success\"\n                });\n            } catch (error) {\n                console.error(\"Failed to copy link:\", error);\n            }\n        }\n    };\n    const toggleFavorite = ()=>{\n        setIsFavorite(!isFavorite);\n    // TODO: Implement favorite functionality\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-8 bg-gray-300 rounded w-1/4 mb-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-96 bg-gray-300 rounded-xl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 bg-gray-300 rounded w-3/4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-gray-300 rounded w-1/2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-20 bg-gray-300 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !ad) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                children: error || \"Ad not found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.back(),\n                                className: \"text-primary-blue hover:text-primary-blue/80\",\n                                children: \"Go back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n            lineNumber: 185,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex items-center space-x-2 text-sm text-gray-500 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/\",\n                                className: \"hover:text-primary-blue transition-colors font-medium\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"›\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/ads\",\n                                className: \"hover:text-primary-blue transition-colors font-medium\",\n                                children: \"All Ads\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"›\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/category/\".concat((_ad_category = ad.category) === null || _ad_category === void 0 ? void 0 : _ad_category.slug),\n                                className: \"hover:text-primary-blue transition-colors font-medium\",\n                                children: (_ad_category1 = ad.category) === null || _ad_category1 === void 0 ? void 0 : _ad_category1.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"›\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-900 truncate font-semibold\",\n                                children: ad.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl md:text-3xl font-bold font-heading text-gray-900 mb-3\",\n                                children: ad.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: ad.location\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.formatDate)(ad.created_at)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this),\n                                    isOwnAd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    ad.view_count || 0,\n                                                    \" views\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShare,\n                                        className: \"flex items-center px-4 py-2 bg-white text-gray-600 hover:bg-gray-50 border border-gray-300 rounded-lg transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Share\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleFavorite,\n                                        className: \"flex items-center px-4 py-2 rounded-lg transition-colors duration-200 \".concat(isFavorite ? \"bg-red-500 text-white hover:bg-red-600\" : \"bg-white text-gray-600 hover:bg-red-50 hover:text-red-600 border border-gray-300\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2 \".concat(isFavorite ? \"fill-current\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Save ad\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-4 gap-6 lg:gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_10__.PremiumCard, {\n                                        variant: \"premium\",\n                                        padding: \"none\",\n                                        className: \"mb-6 overflow-hidden\",\n                                        children: ad.ad_images && ad.ad_images.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-[400px] md:h-[500px] bg-gray-100\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: (_ad_ad_images_currentImageIndex = ad.ad_images[currentImageIndex]) === null || _ad_ad_images_currentImageIndex === void 0 ? void 0 : _ad_ad_images_currentImageIndex.image_url,\n                                                        alt: ad.title,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    ad.ad_images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 right-4 bg-black/80 backdrop-blur-sm text-white px-3 py-2 rounded-lg text-sm font-medium\",\n                                                        children: [\n                                                            currentImageIndex + 1,\n                                                            \" / \",\n                                                            ad.ad_images.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    ad.ad_images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handlePrevImage,\n                                                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm text-gray-800 p-3 rounded-lg shadow-lg hover:bg-white transition-colors duration-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleNextImage,\n                                                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm text-gray-800 p-3 rounded-lg shadow-lg hover:bg-white transition-colors duration-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 315,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-[400px] md:h-[500px] bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl md:text-6xl mb-4\",\n                                                        children: \"\\uD83D\\uDCF7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-base md:text-lg font-medium\",\n                                                        children: \"No images available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Contact seller for more photos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 13\n                                    }, this),\n                                    ad.ad_images && ad.ad_images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-3 overflow-x-auto pb-2 custom-scrollbar\",\n                                        children: ad.ad_images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setCurrentImageIndex(index),\n                                                className: \"flex-shrink-0 w-20 h-20 overflow-hidden border-2 rounded-lg transition-all duration-200 \".concat(index === currentImageIndex ? \"border-primary-blue shadow-md\" : \"border-gray-200 hover:border-primary-blue/50\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: image.image_url,\n                                                    alt: \"\".concat(ad.title, \" \").concat(index + 1),\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sticky top-6 space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                                            children: ad.main_category !== \"jobs\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-600 mb-2\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.formatCurrency)(ad.price)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2 mb-3\",\n                                                        children: [\n                                                            ad.negotiable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Negotiable\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            ad.main_category === \"rent\" && ad.rental_type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\",\n                                                                children: [\n                                                                    \"per \",\n                                                                    ad.rental_type === \"daily\" ? \"day\" : ad.rental_type === \"monthly\" ? \"month\" : \"year\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-bold text-purple-600 mb-2\",\n                                                        children: ad.employer_name || \"Job Opportunity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    (ad.salary_range_from || ad.salary_range_to) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-base font-semibold text-green-600 mb-2\",\n                                                        children: [\n                                                            \"Rs \",\n                                                            ((_ad_salary_range_from = ad.salary_range_from) === null || _ad_salary_range_from === void 0 ? void 0 : _ad_salary_range_from.toLocaleString()) || \"0\",\n                                                            \" - Rs \",\n                                                            ((_ad_salary_range_to = ad.salary_range_to) === null || _ad_salary_range_to === void 0 ? void 0 : _ad_salary_range_to.toLocaleString()) || \"0\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs font-normal text-gray-500\",\n                                                                children: \"per month\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-base font-bold text-gray-900 mb-3\",\n                                                    children: \"Contact Seller\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-gradient-to-br from-primary-blue to-secondary-blue rounded-full flex items-center justify-center text-white font-bold text-sm mr-3\",\n                                                            children: ((_ad_user = ad.user) === null || _ad_user === void 0 ? void 0 : (_ad_user_full_name = _ad_user.full_name) === null || _ad_user_full_name === void 0 ? void 0 : _ad_user_full_name.charAt(0)) || ((_ad_user1 = ad.user) === null || _ad_user1 === void 0 ? void 0 : (_ad_user_email = _ad_user1.email) === null || _ad_user_email === void 0 ? void 0 : _ad_user_email.charAt(0)) || \"U\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-semibold text-gray-900 text-sm\",\n                                                                    children: ((_ad_user2 = ad.user) === null || _ad_user2 === void 0 ? void 0 : _ad_user2.full_name) || \"Anonymous\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"Member since \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.formatDate)(((_ad_user3 = ad.user) === null || _ad_user3 === void 0 ? void 0 : _ad_user3.created_at) || ad.created_at)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setIsChatModalOpen(true),\n                                                                className: \"w-full flex items-center justify-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors duration-200 text-sm font-medium\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Send Message\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            ad.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>window.open(\"tel:\".concat(ad.phone)),\n                                                                className: \"w-full flex items-center justify-center px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200 text-sm font-medium\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Call Now\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-3 bg-blue-50 rounded-lg text-blue-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs mb-2\",\n                                                                children: \"Sign in to contact the seller\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/auth/signin\"),\n                                                                className: \"px-3 py-1 bg-primary-blue text-white rounded text-xs font-medium hover:bg-primary-blue/90 transition-colors duration-200\",\n                                                                children: \"Sign In\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border border-gray-200 rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold font-heading text-gray-900 mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2 text-primary-blue\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Basic Information\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                                        children: [\n                                            ad.main_category !== \"jobs\" && ad.condition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 font-medium text-sm mb-1\",\n                                                        children: \"Condition\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-gray-900 capitalize text-sm\",\n                                                        children: ad.condition\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 font-medium text-sm mb-1\",\n                                                        children: \"Category\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-primary-blue text-sm\",\n                                                        children: (_ad_category2 = ad.category) === null || _ad_category2 === void 0 ? void 0 : _ad_category2.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 15\n                                            }, this),\n                                            ad.subcategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 font-medium text-sm mb-1\",\n                                                        children: \"Subcategory\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-secondary-blue text-sm\",\n                                                        children: ad.subcategory.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 font-medium text-sm mb-1\",\n                                                        children: \"Type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-gray-900 text-sm\",\n                                                        children: ad.main_category === \"jobs\" ? \"Job Posting\" : ad.main_category === \"rent\" ? \"Property Rental\" : \"For Sale\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 15\n                                            }, this),\n                                            ad.main_category !== \"jobs\" && ad.negotiable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 font-medium text-sm mb-1\",\n                                                        children: \"Negotiable\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-green-600 text-sm flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Yes\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 11\n                            }, this),\n                            ad.main_category === \"jobs\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_JobDetailsCard__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                ad: ad\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 13\n                            }, this) : ad.main_category === \"rent\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_RentalDetailsCard__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                ad: ad\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_CategorySpecificDetails__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                ad: ad\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_10__.PremiumCard, {\n                        variant: \"premium\",\n                        className: \"mt-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold font-heading text-gray-900 mb-6 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        className: \"h-6 w-6 mr-3 text-primary-blue\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Description\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose prose-lg max-w-none\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 leading-relaxed whitespace-pre-wrap\",\n                                    children: ad.description || \"No description provided.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_premium__WEBPACK_IMPORTED_MODULE_10__.PremiumCard, {\n                        variant: \"premium\",\n                        className: \"mt-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border rounded-lg p-4 \".concat(ad.main_category === \"jobs\" ? \"bg-purple-50 border-purple-200\" : ad.main_category === \"rent\" ? \"bg-green-50 border-green-200\" : \"bg-blue-50 border-blue-200\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full flex items-center justify-center \".concat(ad.main_category === \"jobs\" ? \"bg-purple-100\" : ad.main_category === \"rent\" ? \"bg-green-100\" : \"bg-blue-100\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronLeft_ChevronRight_Eye_Globe_Heart_MapPin_MessageCircle_Phone_Share2_Shield_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-4 w-4 \".concat(ad.main_category === \"jobs\" ? \"text-purple-600\" : ad.main_category === \"rent\" ? \"text-green-600\" : \"text-blue-600\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-semibold mb-1 \".concat(ad.main_category === \"jobs\" ? \"text-purple-900\" : ad.main_category === \"rent\" ? \"text-green-900\" : \"text-blue-900\"),\n                                                children: ad.main_category === \"jobs\" ? \"Job Application Safety\" : ad.main_category === \"rent\" ? \"Rental Safety Tips\" : \"Stay Alert: Avoid Online Scams\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"text-xs space-y-1 \".concat(ad.main_category === \"jobs\" ? \"text-purple-800\" : ad.main_category === \"rent\" ? \"text-green-800\" : \"text-blue-800\"),\n                                                children: ad.main_category === \"jobs\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Never pay fees to apply for a job or for training materials.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Verify company details and meet in professional settings.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Be cautious of jobs requiring personal financial information upfront.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : ad.main_category === \"rent\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Visit the property in person before making any payments.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Verify ownership documents and get proper rental agreements.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Never transfer money without seeing the property first.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Never share card details or OTPs, and avoid making payments through links sent to you.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Verify seller forms in person before making any payment.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• OKDOI does not offer a delivery service. Buy vigilant!\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-xs mt-2 hover:underline \".concat(ad.main_category === \"jobs\" ? \"text-purple-600 hover:text-purple-800\" : ad.main_category === \"rent\" ? \"text-green-600 hover:text-green-800\" : \"text-blue-600 hover:text-blue-800\"),\n                                                children: \"See all safety tips\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 576,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 md:mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_AdSlider__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            ads: memberAds,\n                            title: \"More ads from this member\",\n                            loading: relatedLoading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                            lineNumber: 590,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                        lineNumber: 589,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 md:mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_AdSlider__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            ads: similarAds,\n                            title: \"Similar ads\",\n                            loading: relatedLoading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                            lineNumber: 599,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                        lineNumber: 598,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                lineNumber: 607,\n                columnNumber: 7\n            }, this),\n            !isOwnAd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isChatModalOpen,\n                onClose: ()=>setIsChatModalOpen(false),\n                ad: ad\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n                lineNumber: 611,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\ad\\\\[id]\\\\page.tsx\",\n        lineNumber: 206,\n        columnNumber: 5\n    }, this);\n}\n_s(SingleAdPage, \"o6poQkHByEasBXYjMH2NOZguUbA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_13__.useAuth\n    ];\n});\n_c = SingleAdPage;\nvar _c;\n$RefreshReg$(_c, \"SingleAdPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ad/[id]/page.tsx\n"));

/***/ })

});