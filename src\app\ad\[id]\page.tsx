'use client'

import { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import {
  ArrowLeft,
  MapPin,
  Calendar,
  Eye,
  Heart,
  Share2,
  Phone,
  MessageCircle,
  Star,
  Shield,
  ChevronLeft,
  ChevronRight,
  Globe,
  Clock,
  Tag,
  CheckCircle
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import AdSlider from '@/components/ads/AdSlider'
import ChatModal from '@/components/ChatModal'
import CategorySpecificDetails from '@/components/ads/CategorySpecificDetails'
import JobDetailsCard from '@/components/ads/JobDetailsCard'
import RentalDetailsCard from '@/components/ads/RentalDetailsCard'
import { PremiumButton, PremiumCard } from '@/components/ui/premium'
import { AdService } from '@/lib/services/ads'
import { AdWithDetails } from '@/types'
import { formatCurrency, formatDate } from '@/lib/utils'
import { useAuth } from '@/contexts/AuthContext'

export default function SingleAdPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const [ad, setAd] = useState<AdWithDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isFavorite, setIsFavorite] = useState(false)
  const [memberAds, setMemberAds] = useState<AdWithDetails[]>([])
  const [similarAds, setSimilarAds] = useState<AdWithDetails[]>([])
  const [relatedLoading, setRelatedLoading] = useState(false)
  const [isChatModalOpen, setIsChatModalOpen] = useState(false)

  // Check if the current user is the owner of this ad
  const isOwnAd = user && ad && user.id === ad.user_id

  useEffect(() => {
    if (params.id) {
      fetchAd(params.id as string)
    }
  }, [params.id])

  const fetchAd = async (id: string) => {
    try {
      setLoading(true)
      const adData = await AdService.getAdById(id)
      
      if (!adData) {
        setError('Ad not found')
        return
      }

      setAd(adData)

      // Fetch related ads after getting the main ad
      await fetchRelatedAds(adData)
    } catch (error) {
      console.error('Error fetching ad:', error)
      setError('Failed to load ad')
    } finally {
      setLoading(false)
    }
  }

  const fetchRelatedAds = async (currentAd: AdWithDetails) => {
    try {
      setRelatedLoading(true)

      // Fetch more ads from the same member (excluding current ad)
      const memberAdsResult = await AdService.getAds(
        { userId: currentAd.user_id, status: 'active' },
        1,
        8
      )
      const filteredMemberAds = memberAdsResult.ads.filter(memberAd => memberAd.id !== currentAd.id)
      setMemberAds(filteredMemberAds)

      // Fetch similar ads from the same subcategory (excluding current ad and member ads)
      const similarAdsResult = await AdService.getAds(
        { subcategory_id: currentAd.subcategory_id, status: 'active' },
        1,
        8
      )
      const filteredSimilarAds = similarAdsResult.ads.filter(
        similarAd => similarAd.id !== currentAd.id && similarAd.user_id !== currentAd.user_id
      )
      setSimilarAds(filteredSimilarAds)

    } catch (error) {
      console.error('Error fetching related ads:', error)
    } finally {
      setRelatedLoading(false)
    }
  }

  const handlePrevImage = () => {
    if (ad?.ad_images && ad.ad_images.length > 0) {
      setCurrentImageIndex((prev) =>
        prev === 0 ? ad.ad_images!.length - 1 : prev - 1
      )
    }
  }

  const handleNextImage = () => {
    if (ad?.ad_images && ad.ad_images.length > 0) {
      setCurrentImageIndex((prev) =>
        prev === ad.ad_images!.length - 1 ? 0 : prev + 1
      )
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: ad?.title,
          text: ad?.description,
          url: window.location.href,
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(window.location.href)
        // Use premium toast notification instead of alert
        const { showAlert } = await import('@/components/ui/ConfirmationDialog')
        await showAlert({
          title: 'Success',
          message: 'Link copied to clipboard!',
          variant: 'success'
        })
      } catch (error) {
        console.error('Failed to copy link:', error)
      }
    }
  }

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite)
    // TODO: Implement favorite functionality
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="h-96 bg-gray-300 rounded-xl"></div>
              <div className="space-y-4">
                <div className="h-8 bg-gray-300 rounded w-3/4"></div>
                <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                <div className="h-20 bg-gray-300 rounded"></div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  if (error || !ad) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {error || 'Ad not found'}
            </h1>
            <button
              onClick={() => router.back()}
              className="text-primary-blue hover:text-primary-blue/80"
            >
              Go back
            </button>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-24">
        {/* Breadcrumb Navigation */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-6">
          <a
            href="/"
            className="hover:text-primary-blue transition-colors font-medium"
          >
            Home
          </a>
          <span>›</span>
          <a
            href="/ads"
            className="hover:text-primary-blue transition-colors font-medium"
          >
            All Ads
          </a>
          <span>›</span>
          <a
            href={`/category/${ad.category?.slug}`}
            className="hover:text-primary-blue transition-colors font-medium"
          >
            {ad.category?.name}
          </a>
          <span>›</span>
          <span className="text-gray-900 truncate font-semibold">{ad.title}</span>
        </nav>

        {/* Title and Basic Info - ikman.lk style */}
        <div className="mb-6">
          <h1 className="text-2xl md:text-3xl font-bold font-heading text-gray-900 mb-3">
            {ad.title}
          </h1>
          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
            <div className="flex items-center">
              <MapPin className="h-4 w-4 mr-1" />
              <span>{ad.location}</span>
            </div>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              <span>{formatDate(ad.created_at)}</span>
            </div>
            {isOwnAd && (
              <div className="flex items-center">
                <Eye className="h-4 w-4 mr-1" />
                <span>{ad.view_count || 0} views</span>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-3 mb-6">
            <button
              onClick={handleShare}
              className="flex items-center px-4 py-2 bg-white text-gray-600 hover:bg-gray-50 border border-gray-300 rounded-lg transition-colors duration-200"
            >
              <Share2 className="h-4 w-4 mr-2" />
              <span className="font-medium">Share</span>
            </button>
            <button
              onClick={toggleFavorite}
              className={`flex items-center px-4 py-2 rounded-lg transition-colors duration-200 ${
                isFavorite
                  ? 'bg-red-500 text-white hover:bg-red-600'
                  : 'bg-white text-gray-600 hover:bg-red-50 hover:text-red-600 border border-gray-300'
              }`}
            >
              <Heart className={`h-4 w-4 mr-2 ${isFavorite ? 'fill-current' : ''}`} />
              <span className="font-medium">Save ad</span>
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 lg:gap-8">
          {/* Image Gallery - ikman.lk style */}
          <div className="lg:col-span-3">
            {/* Main Image */}
            <PremiumCard variant="premium" padding="none" className="mb-6 overflow-hidden">
              {ad.ad_images && ad.ad_images.length > 0 ? (
                <div className="relative">
                  <div className="relative h-[400px] md:h-[500px] bg-gray-100">
                    <img
                      src={ad.ad_images[currentImageIndex]?.image_url}
                      alt={ad.title}
                      className="w-full h-full object-cover"
                    />

                    {/* Image Counter */}
                    {ad.ad_images.length > 1 && (
                      <div className="absolute top-4 right-4 bg-black/80 backdrop-blur-sm text-white px-3 py-2 rounded-lg text-sm font-medium">
                        {currentImageIndex + 1} / {ad.ad_images.length}
                      </div>
                    )}

                    {/* Navigation Arrows */}
                    {ad.ad_images.length > 1 && (
                      <>
                        <button
                          onClick={handlePrevImage}
                          className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm text-gray-800 p-3 rounded-lg shadow-lg hover:bg-white transition-colors duration-200"
                        >
                          <ChevronLeft className="h-5 w-5" />
                        </button>
                        <button
                          onClick={handleNextImage}
                          className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm text-gray-800 p-3 rounded-lg shadow-lg hover:bg-white transition-colors duration-200"
                        >
                          <ChevronRight className="h-5 w-5" />
                        </button>
                      </>
                    )}
                  </div>
                </div>
              ) : (
                <div className="h-[400px] md:h-[500px] bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <div className="text-4xl md:text-6xl mb-4">📷</div>
                    <div className="text-base md:text-lg font-medium">No images available</div>
                    <div className="text-sm">Contact seller for more photos</div>
                  </div>
                </div>
              )}
            </PremiumCard>

            {/* Thumbnail Gallery */}
            {ad.ad_images && ad.ad_images.length > 1 && (
              <div className="flex space-x-3 overflow-x-auto pb-2 custom-scrollbar">
                {ad.ad_images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`flex-shrink-0 w-20 h-20 overflow-hidden border-2 rounded-lg transition-all duration-200 ${
                      index === currentImageIndex
                        ? 'border-primary-blue shadow-md'
                        : 'border-gray-200 hover:border-primary-blue/50'
                    }`}
                  >
                    <img
                      src={image.image_url}
                      alt={`${ad.title} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            )}

          </div>

          {/* Sidebar - ikman.lk style */}
          <div className="lg:col-span-1">
            <div className="sticky top-6 space-y-4">
              {/* Price Card */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                {ad.main_category !== 'jobs' ? (
                  <div>
                    <div className="text-2xl font-bold text-green-600 mb-2">
                      {formatCurrency(ad.price)}
                    </div>
                    <div className="flex flex-wrap gap-2 mb-3">
                      {ad.negotiable && (
                        <span className="inline-flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">
                          <Tag className="h-3 w-3 mr-1" />
                          Negotiable
                        </span>
                      )}
                      {ad.main_category === 'rent' && ad.rental_type && (
                        <span className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">
                          per {ad.rental_type === 'daily' ? 'day' : ad.rental_type === 'monthly' ? 'month' : 'year'}
                        </span>
                      )}
                    </div>
                  </div>
                ) : (
                  <div>
                    <div className="text-lg font-bold text-purple-600 mb-2">
                      {ad.employer_name || 'Job Opportunity'}
                    </div>
                    {(ad.salary_range_from || ad.salary_range_to) && (
                      <div className="text-base font-semibold text-green-600 mb-2">
                        Rs {ad.salary_range_from?.toLocaleString() || '0'} - Rs {ad.salary_range_to?.toLocaleString() || '0'}
                        <div className="text-xs font-normal text-gray-500">per month</div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Contact Seller Card */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-base font-bold text-gray-900 mb-3">Contact Seller</h3>

                {/* Seller Info */}
                <div className="flex items-center mb-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-primary-blue to-secondary-blue rounded-full flex items-center justify-center text-white font-bold text-sm mr-3">
                    {ad.user?.full_name?.charAt(0) || ad.user?.email?.charAt(0) || 'U'}
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 text-sm">{ad.user?.full_name || 'Anonymous'}</div>
                    <div className="text-xs text-gray-500">Member since {formatDate(ad.user?.created_at || ad.created_at)}</div>
                  </div>
                </div>

                {/* Contact Actions */}
                <div className="space-y-2">
                  {user ? (
                    <>
                      <button
                        onClick={() => setIsChatModalOpen(true)}
                        className="w-full flex items-center justify-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors duration-200 text-sm font-medium"
                      >
                        <MessageCircle className="h-4 w-4 mr-2" />
                        Send Message
                      </button>

                      {ad.phone && (
                        <button
                          onClick={() => window.open(`tel:${ad.phone}`)}
                          className="w-full flex items-center justify-center px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200 text-sm font-medium"
                        >
                          <Phone className="h-4 w-4 mr-2" />
                          Call Now
                        </button>
                      )}
                    </>
                  ) : (
                    <div className="text-center p-3 bg-blue-50 rounded-lg text-blue-800">
                      <p className="text-xs mb-2">Sign in to contact the seller</p>
                      <button
                        onClick={() => router.push('/auth/signin')}
                        className="px-3 py-1 bg-primary-blue text-white rounded text-xs font-medium hover:bg-primary-blue/90 transition-colors duration-200"
                      >
                        Sign In
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* Stay Alert Warning Section */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h3 className="text-base font-bold text-yellow-800 mb-3 flex items-center">
                  <Shield className="h-4 w-4 mr-2" />
                  Stay Alert
                </h3>
                <div className="space-y-2 text-xs text-yellow-700">
                  <p>• Never share card details or OTPs, and avoid making payments through links sent to you.</p>
                  <p>• Verify items before purchase before making any payment.</p>
                  <p>• OKDOI does not offer a delivery service.</p>
                  <p>• Stay safe!</p>
                </div>
              </div>
            </div>
          </div>
            {/* Basic Information */}
            <div className="bg-white border border-gray-200 rounded-lg p-6 mt-8">
              <h3 className="text-lg font-bold font-heading text-gray-900 mb-4 flex items-center">
                <Shield className="h-5 w-5 mr-2 text-primary-blue" />
                Basic Information
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {ad.main_category !== 'jobs' && ad.condition && (
                  <div className="flex flex-col">
                    <span className="text-gray-600 font-medium text-sm mb-1">Condition</span>
                    <span className="font-semibold text-gray-900 capitalize text-sm">
                      {ad.condition}
                    </span>
                  </div>
                )}
                <div className="flex flex-col">
                  <span className="text-gray-600 font-medium text-sm mb-1">Category</span>
                  <span className="font-semibold text-primary-blue text-sm">{ad.category?.name}</span>
                </div>
                {ad.subcategory && (
                  <div className="flex flex-col">
                    <span className="text-gray-600 font-medium text-sm mb-1">Subcategory</span>
                    <span className="font-semibold text-secondary-blue text-sm">{ad.subcategory.name}</span>
                  </div>
                )}
                <div className="flex flex-col">
                  <span className="text-gray-600 font-medium text-sm mb-1">Type</span>
                  <span className="font-semibold text-gray-900 text-sm">
                    {ad.main_category === 'jobs' ? 'Job Posting' :
                     ad.main_category === 'rent' ? 'Property Rental' :
                     'For Sale'}
                  </span>
                </div>
                {ad.main_category !== 'jobs' && ad.negotiable && (
                  <div className="flex flex-col">
                    <span className="text-gray-600 font-medium text-sm mb-1">Negotiable</span>
                    <span className="font-semibold text-green-600 text-sm flex items-center">
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Yes
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Product Specifications */}
            <div className="mt-6">
              {ad.main_category === 'jobs' ? (
                <JobDetailsCard ad={ad} />
              ) : ad.main_category === 'rent' ? (
                <RentalDetailsCard ad={ad} />
              ) : (
                <CategorySpecificDetails ad={ad} />
              )}
            </div>

            {/* Description Section */}
            <div className="bg-white border border-gray-200 rounded-lg p-6 mt-6">
              <h2 className="text-lg font-bold font-heading text-gray-900 mb-4 flex items-center">
                <Globe className="h-5 w-5 mr-2 text-primary-blue" />
                Description
              </h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                  {ad.description || 'No description provided.'}
                </p>
              </div>
            </div>

            {/* More ads from this member */}
            <div className="mt-8">
              <AdSlider
                ads={memberAds}
                title="More ads from this member"
                loading={relatedLoading}
              />
            </div>

            {/* Similar ads */}
            <div className="mt-8">
              <AdSlider
                ads={similarAds}
                title="Similar ads"
                loading={relatedLoading}
              />
            </div>
          </div>
      </div>

      <Footer />

      {/* Chat Modal - Only show if not own ad */}
      {!isOwnAd && (
        <ChatModal
          isOpen={isChatModalOpen}
          onClose={() => setIsChatModalOpen(false)}
          ad={ad}
        />
      )}
    </div>
  )
}
