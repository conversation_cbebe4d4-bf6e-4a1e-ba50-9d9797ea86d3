'use client'

import { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import {
  ArrowLeft,
  MapPin,
  Calendar,
  Eye,
  Heart,
  Share2,
  Phone,
  MessageCircle,
  Star,
  Shield,
  ChevronLeft,
  ChevronRight,
  Globe,
  Clock,
  Tag,
  CheckCircle
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import AdSlider from '@/components/ads/AdSlider'
import ChatModal from '@/components/ChatModal'
import CategorySpecificDetails from '@/components/ads/CategorySpecificDetails'
import JobDetailsCard from '@/components/ads/JobDetailsCard'
import RentalDetailsCard from '@/components/ads/RentalDetailsCard'
import { PremiumButton, PremiumCard } from '@/components/ui/premium'
import { AdService } from '@/lib/services/ads'
import { AdWithDetails } from '@/types'
import { formatCurrency, formatDate } from '@/lib/utils'
import { useAuth } from '@/contexts/AuthContext'

export default function SingleAdPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const [ad, setAd] = useState<AdWithDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isFavorite, setIsFavorite] = useState(false)
  const [memberAds, setMemberAds] = useState<AdWithDetails[]>([])
  const [similarAds, setSimilarAds] = useState<AdWithDetails[]>([])
  const [relatedLoading, setRelatedLoading] = useState(false)
  const [isChatModalOpen, setIsChatModalOpen] = useState(false)

  // Check if the current user is the owner of this ad
  const isOwnAd = user && ad && user.id === ad.user_id

  useEffect(() => {
    if (params.id) {
      fetchAd(params.id as string)
    }
  }, [params.id])

  const fetchAd = async (id: string) => {
    try {
      setLoading(true)
      const adData = await AdService.getAdById(id)
      
      if (!adData) {
        setError('Ad not found')
        return
      }

      setAd(adData)

      // Fetch related ads after getting the main ad
      await fetchRelatedAds(adData)
    } catch (error) {
      console.error('Error fetching ad:', error)
      setError('Failed to load ad')
    } finally {
      setLoading(false)
    }
  }

  const fetchRelatedAds = async (currentAd: AdWithDetails) => {
    try {
      setRelatedLoading(true)

      // Fetch more ads from the same member (excluding current ad)
      const memberAdsResult = await AdService.getAds(
        { userId: currentAd.user_id, status: 'active' },
        1,
        8
      )
      const filteredMemberAds = memberAdsResult.ads.filter(memberAd => memberAd.id !== currentAd.id)
      setMemberAds(filteredMemberAds)

      // Fetch similar ads from the same subcategory (excluding current ad and member ads)
      const similarAdsResult = await AdService.getAds(
        { subcategory_id: currentAd.subcategory_id, status: 'active' },
        1,
        8
      )
      const filteredSimilarAds = similarAdsResult.ads.filter(
        similarAd => similarAd.id !== currentAd.id && similarAd.user_id !== currentAd.user_id
      )
      setSimilarAds(filteredSimilarAds)

    } catch (error) {
      console.error('Error fetching related ads:', error)
    } finally {
      setRelatedLoading(false)
    }
  }

  const handlePrevImage = () => {
    if (ad?.ad_images && ad.ad_images.length > 0) {
      setCurrentImageIndex((prev) =>
        prev === 0 ? ad.ad_images!.length - 1 : prev - 1
      )
    }
  }

  const handleNextImage = () => {
    if (ad?.ad_images && ad.ad_images.length > 0) {
      setCurrentImageIndex((prev) =>
        prev === ad.ad_images!.length - 1 ? 0 : prev + 1
      )
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: ad?.title,
          text: ad?.description,
          url: window.location.href,
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(window.location.href)
        // Use premium toast notification instead of alert
        const { showAlert } = await import('@/components/ui/ConfirmationDialog')
        await showAlert({
          title: 'Success',
          message: 'Link copied to clipboard!',
          variant: 'success'
        })
      } catch (error) {
        console.error('Failed to copy link:', error)
      }
    }
  }

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite)
    // TODO: Implement favorite functionality
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="h-96 bg-gray-300 rounded-xl"></div>
              <div className="space-y-4">
                <div className="h-8 bg-gray-300 rounded w-3/4"></div>
                <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                <div className="h-20 bg-gray-300 rounded"></div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  if (error || !ad) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {error || 'Ad not found'}
            </h1>
            <button
              onClick={() => router.back()}
              className="text-primary-blue hover:text-primary-blue/80"
            >
              Go back
            </button>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-24">
        {/* Breadcrumb Navigation */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-6">
          <a
            href="/"
            className="hover:text-primary-blue transition-colors font-medium"
          >
            Home
          </a>
          <span>›</span>
          <a
            href="/ads"
            className="hover:text-primary-blue transition-colors font-medium"
          >
            All Ads
          </a>
          <span>›</span>
          <a
            href={`/category/${ad.category?.slug}`}
            className="hover:text-primary-blue transition-colors font-medium"
          >
            {ad.category?.name}
          </a>
          <span>›</span>
          <span className="text-gray-900 truncate font-semibold">{ad.title}</span>
        </nav>

        {/* Title and Actions */}
        <div className="mb-10">
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
            <div className="flex-1">
              <h1 className="text-2xl md:text-3xl font-bold font-heading text-gray-900 mb-6">
                {ad.title}
              </h1>
              <div className="flex flex-wrap items-center gap-3 text-sm">
                <div className="flex items-center bg-white px-4 py-2 shadow-sm border border-gray-200 rounded-lg">
                  <MapPin className="h-4 w-4 mr-2 text-primary-blue" />
                  <span className="font-medium text-gray-700">{ad.location}</span>
                </div>
                <div className="flex items-center bg-white px-4 py-2 shadow-sm border border-gray-200 rounded-lg">
                  <Calendar className="h-4 w-4 mr-2 text-green-600" />
                  <span className="font-medium text-gray-700">{formatDate(ad.created_at)}</span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3">
              <button
                onClick={handleShare}
                className="flex items-center px-4 py-2 bg-white text-gray-600 hover:bg-primary-blue hover:text-white border border-gray-200 rounded-lg shadow-sm transition-colors duration-200"
              >
                <Share2 className="h-4 w-4 mr-2" />
                <span className="font-medium hidden sm:inline">Share</span>
              </button>
              <button
                onClick={toggleFavorite}
                className={`flex items-center px-4 py-2 rounded-lg shadow-sm transition-colors duration-200 ${
                  isFavorite
                    ? 'bg-red-500 text-white hover:bg-red-600'
                    : 'bg-white text-gray-600 hover:bg-red-50 hover:text-red-600 border border-gray-200'
                }`}
              >
                <Heart className={`h-4 w-4 mr-2 ${isFavorite ? 'fill-current' : ''}`} />
                <span className="font-medium hidden sm:inline">Save ad</span>
              </button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-10">
          {/* Image Gallery */}
          <div className="lg:col-span-2">
            {/* Main Image */}
            <PremiumCard variant="premium" padding="none" className="mb-6 overflow-hidden">
              {ad.ad_images && ad.ad_images.length > 0 ? (
                <div className="relative">
                  <div className="relative h-[400px] md:h-[500px] bg-gray-100">
                    <img
                      src={ad.ad_images[currentImageIndex]?.image_url}
                      alt={ad.title}
                      className="w-full h-full object-cover"
                    />

                    {/* Image Counter */}
                    {ad.ad_images.length > 1 && (
                      <div className="absolute top-4 right-4 bg-black/80 backdrop-blur-sm text-white px-3 py-2 rounded-lg text-sm font-medium">
                        {currentImageIndex + 1} / {ad.ad_images.length}
                      </div>
                    )}

                    {/* Navigation Arrows */}
                    {ad.ad_images.length > 1 && (
                      <>
                        <button
                          onClick={handlePrevImage}
                          className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm text-gray-800 p-3 rounded-lg shadow-lg hover:bg-white transition-colors duration-200"
                        >
                          <ChevronLeft className="h-5 w-5" />
                        </button>
                        <button
                          onClick={handleNextImage}
                          className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm text-gray-800 p-3 rounded-lg shadow-lg hover:bg-white transition-colors duration-200"
                        >
                          <ChevronRight className="h-5 w-5" />
                        </button>
                      </>
                    )}
                  </div>
                </div>
              ) : (
                <div className="h-[400px] md:h-[500px] bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <div className="text-4xl md:text-6xl mb-4">📷</div>
                    <div className="text-base md:text-lg font-medium">No images available</div>
                    <div className="text-sm">Contact seller for more photos</div>
                  </div>
                </div>
              )}
            </PremiumCard>

            {/* Thumbnail Gallery */}
            {ad.ad_images && ad.ad_images.length > 1 && (
              <div className="flex space-x-3 overflow-x-auto pb-2 custom-scrollbar">
                {ad.ad_images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`flex-shrink-0 w-20 h-20 overflow-hidden border-2 rounded-lg transition-all duration-200 ${
                      index === currentImageIndex
                        ? 'border-primary-blue shadow-md'
                        : 'border-gray-200 hover:border-primary-blue/50'
                    }`}
                  >
                    <img
                      src={image.image_url}
                      alt={`${ad.title} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            )}

            {/* Basic Information and Product Specifications - Moved from Sidebar */}
            <div className="mt-8 space-y-6">
              {/* Basic Details */}
              <PremiumCard variant="premium">
                <h3 className="text-lg font-bold font-heading text-gray-900 mb-4 flex items-center">
                  <Shield className="h-5 w-5 mr-2 text-primary-blue" />
                  Basic Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {ad.main_category !== 'jobs' && ad.condition && (
                    <div className="flex flex-col p-3 bg-gray-50 rounded-lg">
                      <span className="text-gray-600 font-medium text-sm mb-1">Condition</span>
                      <span className="font-bold text-gray-900 capitalize bg-green-100 text-green-800 px-3 py-1 rounded-lg text-sm w-fit">
                        {ad.condition}
                      </span>
                    </div>
                  )}
                  <div className="flex flex-col p-3 bg-gray-50 rounded-lg">
                    <span className="text-gray-600 font-medium text-sm mb-1">Category</span>
                    <span className="font-bold text-primary-blue">{ad.category?.name}</span>
                  </div>
                  {ad.subcategory && (
                    <div className="flex flex-col p-3 bg-gray-50 rounded-lg">
                      <span className="text-gray-600 font-medium text-sm mb-1">Subcategory</span>
                      <span className="font-bold text-secondary-blue">{ad.subcategory.name}</span>
                    </div>
                  )}
                  <div className="flex flex-col p-3 bg-gray-50 rounded-lg">
                    <span className="text-gray-600 font-medium text-sm mb-1">Type</span>
                    <span className="font-bold text-gray-900 bg-blue-100 text-blue-800 px-3 py-1 rounded-lg text-sm w-fit">
                      {ad.main_category === 'jobs' ? 'Job Posting' :
                       ad.main_category === 'rent' ? 'Property Rental' :
                       'For Sale'}
                    </span>
                  </div>
                  {ad.main_category !== 'jobs' && ad.negotiable && (
                    <div className="flex flex-col p-3 bg-gray-50 rounded-lg">
                      <span className="text-gray-600 font-medium text-sm mb-1">Negotiable</span>
                      <span className="font-bold text-green-600 bg-green-100 px-3 py-1 rounded-lg text-sm flex items-center w-fit">
                        <CheckCircle className="h-4 w-4 mr-1" />
                        Yes
                      </span>
                    </div>
                  )}
                </div>
              </PremiumCard>

              {/* Category-Specific Details */}
              {ad.main_category === 'jobs' ? (
                <JobDetailsCard ad={ad} />
              ) : ad.main_category === 'rent' ? (
                <RentalDetailsCard ad={ad} />
              ) : (
                <CategorySpecificDetails ad={ad} />
              )}
            </div>

          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Price Card */}
            <PremiumCard variant="premium">
              {ad.main_category !== 'jobs' ? (
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold font-heading text-green-600 mb-3">
                    {formatCurrency(ad.price)}
                  </div>
                  {ad.negotiable && (
                    <div className="inline-flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-lg text-sm font-medium mb-2">
                      <Tag className="h-4 w-4 mr-1" />
                      Negotiable
                    </div>
                  )}
                  {ad.main_category === 'rent' && ad.rental_type && (
                    <div className="text-sm text-gray-600 font-medium">
                      per {ad.rental_type === 'daily' ? 'day' : ad.rental_type === 'monthly' ? 'month' : 'year'}
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center">
                  <div className="text-lg md:text-xl font-bold font-heading text-purple-600 mb-2">
                    {ad.employer_name || 'Job Opportunity'}
                  </div>
                  {(ad.salary_range_from || ad.salary_range_to) && (
                    <div className="text-base md:text-lg font-semibold text-green-600 mb-2">
                      Rs {ad.salary_range_from?.toLocaleString() || '0'} - Rs {ad.salary_range_to?.toLocaleString() || '0'}
                      <div className="text-sm font-normal text-gray-500">per month</div>
                    </div>
                  )}
                </div>
              )}

              {/* Ad Stats */}
              <div className="flex items-center justify-center gap-4 md:gap-6 mt-4 pt-4 border-t border-gray-200">
                <div className="text-center">
                  <div className="flex items-center text-sm text-gray-500">
                    <Clock className="h-4 w-4 mr-1 text-blue-500" />
                    <span className="text-xs font-medium">Posted</span>
                  </div>
                </div>
                <div className="text-center">
                  <div className="flex items-center text-sm text-gray-500">
                    <Eye className="h-4 w-4 mr-1 text-purple-500" />
                    <span className="text-xs font-medium">{ad.view_count || 0} views</span>
                  </div>
                </div>
              </div>
            </PremiumCard>

            {/* Contact Seller Card */}
            <PremiumCard variant="premium">
              <h3 className="text-lg font-bold font-heading text-gray-900 mb-4 text-center">Contact Seller</h3>

              {/* Seller Info */}
              <div className="flex items-center mb-4 p-3 bg-gray-50 rounded-lg">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-blue to-secondary-blue rounded-full flex items-center justify-center text-white font-bold text-lg mr-3">
                  {ad.user?.full_name?.charAt(0) || ad.user?.email?.charAt(0) || 'U'}
                </div>
                <div>
                  <div className="font-semibold text-gray-900">{ad.user?.full_name || 'Anonymous'}</div>
                  <div className="text-sm text-gray-500">Member since {formatDate(ad.user?.created_at || ad.created_at)}</div>
                </div>
              </div>

              {/* Contact Actions */}
              <div className="space-y-3">
                {user ? (
                  <>
                    <PremiumButton
                      variant="primary"
                      fullWidth
                      onClick={() => setIsChatModalOpen(true)}
                      icon={<MessageCircle className="h-5 w-5" />}
                    >
                      Send Message
                    </PremiumButton>

                    {ad.phone && (
                      <PremiumButton
                        variant="outline"
                        fullWidth
                        onClick={() => window.open(`tel:${ad.phone}`)}
                        icon={<Phone className="h-5 w-5" />}
                      >
                        Call Now
                      </PremiumButton>
                    )}
                  </>
                ) : (
                  <div className="text-center p-4 bg-blue-50 rounded-lg text-blue-800">
                    <p className="text-sm mb-3">Sign in to contact the seller</p>
                    <PremiumButton
                      variant="primary"
                      size="sm"
                      onClick={() => router.push('/auth/signin')}
                    >
                      Sign In
                    </PremiumButton>
                  </div>
                )}
              </div>
            </PremiumCard>


          </div>
        </div>

        {/* Description Section */}
        <PremiumCard variant="premium" className="mt-12">
          <h2 className="text-2xl font-bold font-heading text-gray-900 mb-6 flex items-center">
            <Globe className="h-6 w-6 mr-3 text-primary-blue" />
            Description
          </h2>
          <div className="prose prose-lg max-w-none">
            <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
              {ad.description || 'No description provided.'}
            </p>
          </div>
        </PremiumCard>

        {/* Safety Alert Section */}
        <PremiumCard variant="premium" className="mt-10">
          <div className={`border rounded-lg p-4 ${
            ad.main_category === 'jobs' ? 'bg-purple-50 border-purple-200' :
            ad.main_category === 'rent' ? 'bg-green-50 border-green-200' :
            'bg-blue-50 border-blue-200'
          }`}>
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  ad.main_category === 'jobs' ? 'bg-purple-100' :
                  ad.main_category === 'rent' ? 'bg-green-100' :
                  'bg-blue-100'
                }`}>
                  <Shield className={`h-4 w-4 ${
                    ad.main_category === 'jobs' ? 'text-purple-600' :
                    ad.main_category === 'rent' ? 'text-green-600' :
                    'text-blue-600'
                  }`} />
                </div>
              </div>
              <div className="ml-3">
                <h4 className={`text-sm font-semibold mb-1 ${
                  ad.main_category === 'jobs' ? 'text-purple-900' :
                  ad.main_category === 'rent' ? 'text-green-900' :
                  'text-blue-900'
                }`}>
                  {ad.main_category === 'jobs' ? 'Job Application Safety' :
                   ad.main_category === 'rent' ? 'Rental Safety Tips' :
                   'Stay Alert: Avoid Online Scams'}
                </h4>
                <ul className={`text-xs space-y-1 ${
                  ad.main_category === 'jobs' ? 'text-purple-800' :
                  ad.main_category === 'rent' ? 'text-green-800' :
                  'text-blue-800'
                }`}>
                  {ad.main_category === 'jobs' ? (
                    <>
                      <li>• Never pay fees to apply for a job or for training materials.</li>
                      <li>• Verify company details and meet in professional settings.</li>
                      <li>• Be cautious of jobs requiring personal financial information upfront.</li>
                    </>
                  ) : ad.main_category === 'rent' ? (
                    <>
                      <li>• Visit the property in person before making any payments.</li>
                      <li>• Verify ownership documents and get proper rental agreements.</li>
                      <li>• Never transfer money without seeing the property first.</li>
                    </>
                  ) : (
                    <>
                      <li>• Never share card details or OTPs, and avoid making payments through links sent to you.</li>
                      <li>• Verify seller forms in person before making any payment.</li>
                      <li>• OKDOI does not offer a delivery service. Buy vigilant!</li>
                    </>
                  )}
                </ul>
                <button className={`text-xs mt-2 hover:underline ${
                  ad.main_category === 'jobs' ? 'text-purple-600 hover:text-purple-800' :
                  ad.main_category === 'rent' ? 'text-green-600 hover:text-green-800' :
                  'text-blue-600 hover:text-blue-800'
                }`}>
                  See all safety tips
                </button>
              </div>
            </div>
          </div>
        </PremiumCard>

        {/* More ads from this member */}
        <div className="mt-12 md:mt-16">
          <AdSlider
            ads={memberAds}
            title="More ads from this member"
            loading={relatedLoading}
          />
        </div>

        {/* Similar ads */}
        <div className="mt-12 md:mt-16">
          <AdSlider
            ads={similarAds}
            title="Similar ads"
            loading={relatedLoading}
          />
        </div>
      </div>

      <Footer />

      {/* Chat Modal - Only show if not own ad */}
      {!isOwnAd && (
        <ChatModal
          isOpen={isChatModalOpen}
          onClose={() => setIsChatModalOpen(false)}
          ad={ad}
        />
      )}
    </div>
  )
}
